import { BookingType, BookingStatus, Booking } from "@prisma/client";
import prisma from "../db.server";

export type { Booking, BookingType, BookingStatus };

export interface CreateBookingData {
  title: string;
  description?: string;
  startDateTime: Date;
  endDateTime: Date;
  bookingType: BookingType;
  customerName: string;
  customerEmail: string;
  customerPhone?: string;
  shop: string;
  price?: number;
  currency?: string;
  notes?: string;
}

export interface UpdateBookingData {
  title?: string;
  description?: string;
  startDateTime?: Date;
  endDateTime?: Date;
  status?: BookingStatus;
  customerName?: string;
  customerEmail?: string;
  customerPhone?: string;
  price?: number;
  currency?: string;
  notes?: string;
}

/**
 * Create a new booking
 */
export async function createBooking(data: CreateBookingData): Promise<Booking> {
  return prisma.booking.create({
    data: {
      ...data,
      price: data.price ? data.price : undefined,
    },
  });
}

/**
 * Get a booking by ID
 */
export async function getBooking(id: string): Promise<Booking | null> {
  return prisma.booking.findUnique({
    where: { id },
  });
}

/**
 * Get all bookings for a shop
 */
export async function getBookingsByShop(shop: string): Promise<Booking[]> {
  return prisma.booking.findMany({
    where: { shop },
    orderBy: { startDateTime: "asc" },
  });
}

/**
 * Get bookings by type for a shop
 */
export async function getBookingsByType(
  shop: string,
  bookingType: BookingType,
): Promise<Booking[]> {
  return prisma.booking.findMany({
    where: {
      shop,
      bookingType,
    },
    orderBy: { startDateTime: "asc" },
  });
}

/**
 * Get bookings by status for a shop
 */
export async function getBookingsByStatus(
  shop: string,
  status: BookingStatus,
): Promise<Booking[]> {
  return prisma.booking.findMany({
    where: {
      shop,
      status,
    },
    orderBy: { startDateTime: "asc" },
  });
}

/**
 * Get bookings within a date range for a shop
 */
export async function getBookingsInDateRange(
  shop: string,
  startDate: Date,
  endDate: Date,
): Promise<Booking[]> {
  return prisma.booking.findMany({
    where: {
      shop,
      startDateTime: {
        gte: startDate,
        lte: endDate,
      },
    },
    orderBy: { startDateTime: "asc" },
  });
}

/**
 * Update a booking
 */
export async function updateBooking(
  id: string,
  data: UpdateBookingData,
): Promise<Booking> {
  return prisma.booking.update({
    where: { id },
    data: {
      ...data,
      price: data.price ? data.price : undefined,
    },
  });
}

/**
 * Delete a booking
 */
export async function deleteBooking(id: string): Promise<Booking> {
  return prisma.booking.delete({
    where: { id },
  });
}

/**
 * Check for booking conflicts (overlapping times for the same shop)
 */
export async function checkBookingConflicts(
  shop: string,
  startDateTime: Date,
  endDateTime: Date,
  excludeBookingId?: string,
): Promise<Booking[]> {
  return prisma.booking.findMany({
    where: {
      shop,
      id: excludeBookingId ? { not: excludeBookingId } : undefined,
      status: { not: BookingStatus.CANCELLED },
      OR: [
        {
          // New booking starts during existing booking
          startDateTime: {
            gte: startDateTime,
            lt: endDateTime,
          },
        },
        {
          // New booking ends during existing booking
          endDateTime: {
            gt: startDateTime,
            lte: endDateTime,
          },
        },
        {
          // New booking completely encompasses existing booking
          AND: [
            { startDateTime: { gte: startDateTime } },
            { endDateTime: { lte: endDateTime } },
          ],
        },
        {
          // Existing booking completely encompasses new booking
          AND: [
            { startDateTime: { lte: startDateTime } },
            { endDateTime: { gte: endDateTime } },
          ],
        },
      ],
    },
  });
}

/**
 * Helper function to create an hourly booking
 */
export async function createHourlyBooking(
  data: Omit<CreateBookingData, "bookingType">,
): Promise<Booking> {
  return createBooking({
    ...data,
    bookingType: BookingType.HOURLY,
  });
}

/**
 * Helper function to create a full day booking
 */
export async function createFullDayBooking(
  data: Omit<CreateBookingData, "bookingType">,
): Promise<Booking> {
  return createBooking({
    ...data,
    bookingType: BookingType.FULL_DAY,
  });
}
