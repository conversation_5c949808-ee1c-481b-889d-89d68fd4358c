# Booking System Documentation

This document describes the booking system implemented for the Shopify app, which supports both hourly and full-day bookings.

## Overview

The booking system is built using Prisma ORM with SQLite database and provides a complete solution for managing bookings in a Shopify app context.

## Features

- ✅ **Hourly Bookings**: Support for time-specific bookings with precise start and end times
- ✅ **Full Day Bookings**: Support for all-day bookings spanning entire days
- ✅ **Booking Status Management**: Track booking lifecycle (PENDING, CONFIRMED, CANCELLED, COMPLETED)
- ✅ **Customer Information**: Store customer details (name, email, phone)
- ✅ **Shopify Integration**: Multi-shop support with shop-specific bookings
- ✅ **Pricing Support**: Decimal precision pricing with currency support
- ✅ **Conflict Detection**: Automatic detection of overlapping bookings
- ✅ **Comprehensive Testing**: Full test suite with TDD approach

## Database Schema

### Booking Model

```prisma
model Booking {
  id            String        @id @default(cuid())
  title         String
  description   String?
  
  // Time fields
  startDateTime DateTime
  endDateTime   DateTime
  bookingType   BookingType
  
  // Status and metadata
  status        BookingStatus @default(PENDING)
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt
  
  // Customer information
  customerName  String
  customerEmail String
  customerPhone String?
  
  // Shopify context
  shop          String        // Shopify shop domain
  
  // Pricing
  price         Decimal?      // Using Decimal for precise currency handling
  currency      String?       @default("USD")
  
  // Additional fields
  notes         String?
  
  @@map("bookings")
}
```

### Enums

```prisma
enum BookingType {
  HOURLY
  FULL_DAY
}

enum BookingStatus {
  PENDING
  CONFIRMED
  CANCELLED
  COMPLETED
}
```

## API Functions

### Core Functions

#### `createBooking(data: CreateBookingData): Promise<Booking>`
Creates a new booking with the provided data.

#### `getBooking(id: string): Promise<Booking | null>`
Retrieves a booking by its ID.

#### `updateBooking(id: string, data: UpdateBookingData): Promise<Booking>`
Updates an existing booking.

#### `deleteBooking(id: string): Promise<Booking>`
Deletes a booking.

### Query Functions

#### `getBookingsByShop(shop: string): Promise<Booking[]>`
Gets all bookings for a specific shop, ordered by start time.

#### `getBookingsByType(shop: string, bookingType: BookingType): Promise<Booking[]>`
Gets bookings filtered by type for a specific shop.

#### `getBookingsByStatus(shop: string, status: BookingStatus): Promise<Booking[]>`
Gets bookings filtered by status for a specific shop.

#### `getBookingsInDateRange(shop: string, startDate: Date, endDate: Date): Promise<Booking[]>`
Gets bookings within a specific date range for a shop.

### Utility Functions

#### `createHourlyBooking(data: Omit<CreateBookingData, 'bookingType'>): Promise<Booking>`
Helper function to create an hourly booking.

#### `createFullDayBooking(data: Omit<CreateBookingData, 'bookingType'>): Promise<Booking>`
Helper function to create a full day booking.

#### `checkBookingConflicts(shop: string, startDateTime: Date, endDateTime: Date, excludeBookingId?: string): Promise<Booking[]>`
Checks for overlapping bookings that would conflict with the specified time range.

## Usage Examples

### Creating an Hourly Booking

```typescript
import { createHourlyBooking } from './app/models/booking.server'

const booking = await createHourlyBooking({
  title: 'Meeting Room A',
  description: 'Team standup meeting',
  startDateTime: new Date('2024-12-01T10:00:00Z'),
  endDateTime: new Date('2024-12-01T11:00:00Z'),
  customerName: 'John Doe',
  customerEmail: '<EMAIL>',
  shop: 'my-shop.myshopify.com',
  price: 50.00
})
```

### Creating a Full Day Booking

```typescript
import { createFullDayBooking } from './app/models/booking.server'

const booking = await createFullDayBooking({
  title: 'Conference Room - All Day Event',
  description: 'Company retreat',
  startDateTime: new Date('2024-12-01T00:00:00Z'),
  endDateTime: new Date('2024-12-01T23:59:59Z'),
  customerName: 'Jane Smith',
  customerEmail: '<EMAIL>',
  shop: 'my-shop.myshopify.com',
  price: 200.00
})
```

### Checking for Conflicts

```typescript
import { checkBookingConflicts } from './app/models/booking.server'

const conflicts = await checkBookingConflicts(
  'my-shop.myshopify.com',
  new Date('2024-12-01T10:30:00Z'),
  new Date('2024-12-01T12:00:00Z')
)

if (conflicts.length > 0) {
  console.log('Booking conflicts detected!')
}
```

## Testing

The system includes comprehensive tests covering:

- ✅ Basic CRUD operations
- ✅ Booking type validation
- ✅ Status management
- ✅ Query functions
- ✅ Conflict detection
- ✅ Data validation
- ✅ Edge cases

### Running Tests

```bash
# Run all tests
npm run test

# Run specific test file
npm run test:run tests/booking.test.ts

# Run tests with UI
npm run test:ui
```

## Database Migrations

The booking system was added via Prisma migration:

```bash
# Generate and apply migration
npx prisma migrate dev --name add_booking_model

# Apply migrations in production
npx prisma migrate deploy
```

## File Structure

```
├── app/models/booking.server.ts    # Server-side booking functions
├── prisma/schema.prisma            # Database schema with Booking model
├── tests/booking.test.ts           # Basic model tests
├── tests/booking.server.test.ts    # Server function tests
├── examples/booking-usage.ts       # Usage examples
└── BOOKING_SYSTEM.md              # This documentation
```

## Integration with Shopify

The booking system is designed to work seamlessly with Shopify apps:

- **Multi-tenant**: Each booking is associated with a specific shop
- **Session Integration**: Works with existing Shopify session management
- **Scalable**: Supports multiple shops with isolated data
- **Extensible**: Easy to add new fields or functionality

## Next Steps

Potential enhancements for the booking system:

1. **Email Notifications**: Send confirmation emails to customers
2. **Calendar Integration**: Sync with external calendar systems
3. **Recurring Bookings**: Support for repeating bookings
4. **Resource Management**: Link bookings to specific resources/rooms
5. **Payment Integration**: Connect with Shopify's payment system
6. **Admin Dashboard**: Create UI for managing bookings
7. **Customer Portal**: Allow customers to view/manage their bookings

## Support

For questions or issues with the booking system, please refer to:

- Prisma documentation: https://www.prisma.io/docs
- Shopify App development: https://shopify.dev/docs/apps
- Test files for usage examples
