// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

// Note that some adapters may set a maximum length for the String type by default, please ensure your strings are long
// enough when changing adapters.
// See https://www.prisma.io/docs/orm/reference/prisma-schema-reference#string for more information
datasource db {
  provider = "sqlite"
  url      = "file:dev.sqlite"
}

model Session {
  id            String    @id
  shop          String
  state         String
  isOnline      <PERSON>an   @default(false)
  scope         String?
  expires       DateTime?
  accessToken   String
  userId        BigInt?
  firstName     String?
  lastName      String?
  email         String?
  accountOwner  Boolean   @default(false)
  locale        String?
  collaborator  <PERSON><PERSON><PERSON>?  @default(false)
  emailVerified <PERSON>olean?  @default(false)
}

model Booking {
  id          String  @id @default(cuid())
  title       String
  description String?

  // Time fields
  startDateTime DateTime
  endDateTime   DateTime
  bookingType   BookingType

  // Status and metadata
  status    BookingStatus @default(PENDING)
  createdAt DateTime      @default(now())
  updatedAt DateTime      @updatedAt

  // Customer information
  customerName  String
  customerEmail String
  customerPhone String?

  // Shopify context
  shop String // Shopify shop domain

  // Pricing
  price    Decimal? // Using Decimal for precise currency handling
  currency String?  @default("USD")

  // Additional fields
  notes String?

  @@map("bookings")
}

enum BookingType {
  HOURLY
  FULL_DAY
}

enum BookingStatus {
  PENDING
  CONFIRMED
  CANCELLED
  COMPLETED
}
