import { beforeEach, beforeAll, afterAll } from "vitest";
import { execSync } from "child_process";
import prisma from "app/db.server";

beforeAll(async () => {
  try {
    // Generate Prisma client for the test database
    execSync("npx prisma generate", { stdio: "inherit" });

    // Push the schema to the test database (creates tables if they don't exist)
    execSync("npx prisma db push --force-reset", { stdio: "inherit" });

    // Connect to the database
    await prisma.$connect();
  } catch (error) {
    console.error("Failed to setup test database:", error);
    throw error;
  }
});

beforeEach(async () => {
  // Clean up the database before each test
  await prisma.booking.deleteMany();
  await prisma.session.deleteMany();
});

afterAll(async () => {
  await prisma.$disconnect();
});

export { prisma };
