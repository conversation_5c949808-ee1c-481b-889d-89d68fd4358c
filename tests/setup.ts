import { beforeEach, beforeAll, afterAll } from "vitest";
import prisma from "app/db.server";

beforeAll(async () => {
  await prisma.$connect();
  // Push the schema to create tables
  await prisma.$executeRaw`PRAGMA foreign_keys = OFF`;

  // Create the tables manually for testing
  await prisma.$executeRaw`
    CREATE TABLE IF NOT EXISTS "Session" (
      "id" TEXT NOT NULL PRIMARY KEY,
      "shop" TEXT NOT NULL,
      "state" TEXT NOT NULL,
      "isOnline" BOOLEAN NOT NULL DEFAULT false,
      "scope" TEXT,
      "expires" DATETIME,
      "accessToken" TEXT NOT NULL,
      "userId" BIGINT,
      "firstName" TEXT,
      "lastName" TEXT,
      "email" TEXT,
      "accountOwner" BOOLEAN NOT NULL DEFAULT false,
      "locale" TEXT,
      "collaborator" BOOLEAN DEFAULT false,
      "emailVerified" BOOLEAN DEFAULT false
    )
  `;

  await prisma.$executeRaw`
    CREATE TABLE IF NOT EXISTS "bookings" (
      "id" TEXT NOT NULL PRIMARY KEY,
      "title" TEXT NOT NULL,
      "description" TEXT,
      "startDateTime" DATETIME NOT NULL,
      "endDateTime" DATETIME NOT NULL,
      "bookingType" TEXT NOT NULL,
      "status" TEXT NOT NULL DEFAULT 'PENDING',
      "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
      "updatedAt" DATETIME NOT NULL,
      "customerName" TEXT NOT NULL,
      "customerEmail" TEXT NOT NULL,
      "customerPhone" TEXT,
      "shop" TEXT NOT NULL,
      "price" DECIMAL,
      "currency" TEXT DEFAULT 'USD',
      "notes" TEXT
    )
  `;
});

beforeEach(async () => {
  // Clean up the database before each test
  await prisma.booking.deleteMany();
  await prisma.session.deleteMany();
});

afterAll(async () => {
  await prisma.$disconnect();
});

export { prisma };
