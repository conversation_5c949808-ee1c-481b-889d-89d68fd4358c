import { describe, it, expect, beforeAll, afterAll, beforeEach } from "vitest";
import { BookingType, BookingStatus } from "@prisma/client";
import { prisma } from "./setup";
import {
  createBooking,
  createHourlyBooking,
  createFullDayBooking,
  getBooking,
  getBookingsByShop,
  getBookingsByType,
  updateBooking,
  deleteBooking,
  checkBookingConflicts,
} from "../app/models/booking.server";

describe("Booking Server Functions", () => {
  beforeAll(async () => {
    await prisma.$connect();
  });

  afterAll(async () => {
    await prisma.$disconnect();
  });

  beforeEach(async () => {
    // Clean up all bookings before
    //  each test
    console.log("Cleaned");

    await prisma.booking.deleteMany();
  });

  describe("createBooking", () => {
    it("should create a booking with all fields", async () => {
      const bookingData = {
        title: "Test Booking",
        description: "A test booking",
        startDateTime: new Date("2024-12-01T10:00:00Z"),
        endDateTime: new Date("2024-12-01T12:00:00Z"),
        bookingType: BookingType.HOURLY,
        customerName: "John Doe",
        customerEmail: "<EMAIL>",
        customerPhone: "+1234567890",
        shop: "test-shop.myshopify.com",
        price: 100,
        currency: "USD",
        notes: "Test notes",
      };

      const booking = await createBooking(bookingData);

      expect(booking.title).toBe(bookingData.title);
      expect(booking.description).toBe(bookingData.description);
      expect(booking.bookingType).toBe(BookingType.HOURLY);
      expect(booking.customerName).toBe(bookingData.customerName);
      expect(booking.price?.toString()).toBe("100");
    });
  });

  describe("Helper functions", () => {
    it("should create hourly booking", async () => {
      const booking = await createHourlyBooking({
        title: "Hourly Test",
        startDateTime: new Date("2024-12-01T10:00:00Z"),
        endDateTime: new Date("2024-12-01T11:00:00Z"),
        customerName: "Test Customer",
        customerEmail: "<EMAIL>",
        shop: "test.myshopify.com",
      });

      expect(booking.bookingType).toBe(BookingType.HOURLY);
    });

    it("should create full day booking", async () => {
      const booking = await createFullDayBooking({
        title: "Full Day Test",
        startDateTime: new Date("2024-12-01T00:00:00Z"),
        endDateTime: new Date("2024-12-01T23:59:59Z"),
        customerName: "Test Customer",
        customerEmail: "<EMAIL>",
        shop: "test.myshopify.com",
      });

      expect(booking.bookingType).toBe(BookingType.FULL_DAY);
    });
  });

  describe("getBooking", () => {
    it("should retrieve a booking by ID", async () => {
      const created = await createBooking({
        title: "Retrieve Test",
        startDateTime: new Date("2024-12-01T10:00:00Z"),
        endDateTime: new Date("2024-12-01T11:00:00Z"),
        bookingType: BookingType.HOURLY,
        customerName: "Test Customer",
        customerEmail: "<EMAIL>",
        shop: "test.myshopify.com",
      });

      const retrieved = await getBooking(created.id);

      expect(retrieved).toBeDefined();
      expect(retrieved?.id).toBe(created.id);
      expect(retrieved?.title).toBe("Retrieve Test");
    });

    it("should return null for non-existent booking", async () => {
      const booking = await getBooking("non-existent-id");
      expect(booking).toBeNull();
    });
  });

  describe("getBookingsByShop", () => {
    it("should return bookings for a specific shop", async () => {
      const shop1 = "shop1.myshopify.com";
      const shop2 = "shop2.myshopify.com";

      await createBooking({
        title: "Shop 1 Booking",
        startDateTime: new Date("2024-12-01T10:00:00Z"),
        endDateTime: new Date("2024-12-01T11:00:00Z"),
        bookingType: BookingType.HOURLY,
        customerName: "Customer 1",
        customerEmail: "<EMAIL>",
        shop: shop1,
      });

      await createBooking({
        title: "Shop 2 Booking",
        startDateTime: new Date("2024-12-01T12:00:00Z"),
        endDateTime: new Date("2024-12-01T13:00:00Z"),
        bookingType: BookingType.HOURLY,
        customerName: "Customer 2",
        customerEmail: "<EMAIL>",
        shop: shop2,
      });

      const shop1Bookings = await getBookingsByShop(shop1);
      console.log("Final: ", shop1Bookings);
      expect(shop1Bookings).toHaveLength(1);
      expect(shop1Bookings[0].title).toBe("Shop 1 Booking");
    });
  });

  describe("getBookingsByType", () => {
    it("should filter bookings by type", async () => {
      const shop = "test.myshopify.com";

      await createHourlyBooking({
        title: "Hourly Booking",
        startDateTime: new Date("2024-12-01T10:00:00Z"),
        endDateTime: new Date("2024-12-01T11:00:00Z"),
        customerName: "Hourly Customer",
        customerEmail: "<EMAIL>",
        shop,
      });

      await createFullDayBooking({
        title: "Full Day Booking",
        startDateTime: new Date("2024-12-02T00:00:00Z"),
        endDateTime: new Date("2024-12-02T23:59:59Z"),
        customerName: "Full Day Customer",
        customerEmail: "<EMAIL>",
        shop,
      });

      const hourlyBookings = await getBookingsByType(shop, BookingType.HOURLY);
      const fullDayBookings = await getBookingsByType(
        shop,
        BookingType.FULL_DAY,
      );

      expect(hourlyBookings).toHaveLength(1);
      expect(fullDayBookings).toHaveLength(1);
      expect(hourlyBookings[0].title).toBe("Hourly Booking");
      expect(fullDayBookings[0].title).toBe("Full Day Booking");
    });
  });

  describe("updateBooking", () => {
    it("should update booking fields", async () => {
      const booking = await createBooking({
        title: "Original Title",
        startDateTime: new Date("2024-12-01T10:00:00Z"),
        endDateTime: new Date("2024-12-01T11:00:00Z"),
        bookingType: BookingType.HOURLY,
        customerName: "Original Customer",
        customerEmail: "<EMAIL>",
        shop: "test.myshopify.com",
      });

      const updated = await updateBooking(booking.id, {
        title: "Updated Title",
        status: BookingStatus.CONFIRMED,
        price: 150,
      });

      expect(updated.title).toBe("Updated Title");
      expect(updated.status).toBe(BookingStatus.CONFIRMED);
      expect(updated.price?.toString()).toBe("150");
      expect(updated.customerName).toBe("Original Customer"); // Should remain unchanged
    });
  });

  describe("checkBookingConflicts", () => {
    it("should detect overlapping bookings", async () => {
      const shop = "test.myshopify.com";

      // Create existing booking from 10:00 to 12:00
      await createBooking({
        title: "Existing Booking",
        startDateTime: new Date("2024-12-01T10:00:00Z"),
        endDateTime: new Date("2024-12-01T12:00:00Z"),
        bookingType: BookingType.HOURLY,
        customerName: "Existing Customer",
        customerEmail: "<EMAIL>",
        shop,
      });

      // Check for conflicts with overlapping time (11:00 to 13:00)
      const conflicts = await checkBookingConflicts(
        shop,
        new Date("2024-12-01T11:00:00Z"),
        new Date("2024-12-01T13:00:00Z"),
      );

      expect(conflicts).toHaveLength(1);
      expect(conflicts[0].title).toBe("Existing Booking");
    });

    it("should not detect conflicts for non-overlapping bookings", async () => {
      const shop = "test.myshopify.com";

      // Create existing booking from 10:00 to 12:00
      await createBooking({
        title: "Existing Booking",
        startDateTime: new Date("2024-12-01T10:00:00Z"),
        endDateTime: new Date("2024-12-01T12:00:00Z"),
        bookingType: BookingType.HOURLY,
        customerName: "Existing Customer",
        customerEmail: "<EMAIL>",
        shop,
      });

      // Check for conflicts with non-overlapping time (13:00 to 14:00)
      const conflicts = await checkBookingConflicts(
        shop,
        new Date("2024-12-01T13:00:00Z"),
        new Date("2024-12-01T14:00:00Z"),
      );

      expect(conflicts).toHaveLength(0);
    });
  });

  describe("deleteBooking", () => {
    it("should delete a booking", async () => {
      const booking = await createBooking({
        title: "To Be Deleted",
        startDateTime: new Date("2024-12-01T10:00:00Z"),
        endDateTime: new Date("2024-12-01T11:00:00Z"),
        bookingType: BookingType.HOURLY,
        customerName: "Delete Customer",
        customerEmail: "<EMAIL>",
        shop: "test.myshopify.com",
      });

      const deleted = await deleteBooking(booking.id);
      expect(deleted.id).toBe(booking.id);

      const retrieved = await getBooking(booking.id);
      expect(retrieved).toBeNull();
    });
  });
});
