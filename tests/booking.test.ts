import { describe, it, expect, beforeAll, afterAll } from "vitest";
import { BookingType, BookingStatus } from "@prisma/client";
import { prisma } from "./setup";

describe("Booking Model", () => {
  beforeAll(async () => {
    // Ensure database is set up
    await prisma.$connect();
  });

  afterAll(async () => {
    await prisma.$disconnect();
  });

  describe("Creating bookings", () => {
    it("should create an hourly booking successfully", async () => {
      const startDateTime = new Date("2024-12-01T10:00:00Z");
      const endDateTime = new Date("2024-12-01T12:00:00Z");

      const booking = await prisma.booking.create({
        data: {
          title: "Meeting Room Booking",
          description: "Team standup meeting",
          startDateTime,
          endDateTime,
          bookingType: BookingType.HOURLY,
          customerName: "John Doe",
          customerEmail: "<EMAIL>",
          customerPhone: "+1234567890",
          shop: "test-shop.myshopify.com",
          price: 50.0,
          currency: "USD",
          notes: "Please prepare projector",
        },
      });

      expect(booking).toBeDefined();
      expect(booking.id).toBeDefined();
      expect(booking.title).toBe("Meeting Room Booking");
      expect(booking.bookingType).toBe(BookingType.HOURLY);
      expect(booking.status).toBe(BookingStatus.PENDING);
      expect(booking.startDateTime).toEqual(startDateTime);
      expect(booking.endDateTime).toEqual(endDateTime);
      expect(booking.customerName).toBe("John Doe");
      expect(booking.customerEmail).toBe("<EMAIL>");
      expect(booking.shop).toBe("test-shop.myshopify.com");
      expect(booking.price?.toString()).toBe("50");
      expect(booking.currency).toBe("USD");
      expect(booking.createdAt).toBeDefined();
      expect(booking.updatedAt).toBeDefined();
    });

    it("should create a full day booking successfully", async () => {
      const startDateTime = new Date("2024-12-01T00:00:00Z");
      const endDateTime = new Date("2024-12-01T23:59:59Z");

      const booking = await prisma.booking.create({
        data: {
          title: "Conference Room Full Day",
          description: "Company retreat",
          startDateTime,
          endDateTime,
          bookingType: BookingType.FULL_DAY,
          customerName: "Jane Smith",
          customerEmail: "<EMAIL>",
          shop: "company-shop.myshopify.com",
          price: 200.0,
        },
      });

      expect(booking).toBeDefined();
      expect(booking.bookingType).toBe(BookingType.FULL_DAY);
      expect(booking.status).toBe(BookingStatus.PENDING);
      expect(booking.startDateTime).toEqual(startDateTime);
      expect(booking.endDateTime).toEqual(endDateTime);
      expect(booking.customerName).toBe("Jane Smith");
      expect(booking.price?.toString()).toBe("200");
      expect(booking.currency).toBe("USD"); // Should default to USD
    });

    it("should create a booking with minimal required fields", async () => {
      const booking = await prisma.booking.create({
        data: {
          title: "Simple Booking",
          startDateTime: new Date("2024-12-01T14:00:00Z"),
          endDateTime: new Date("2024-12-01T15:00:00Z"),
          bookingType: BookingType.HOURLY,
          customerName: "Test Customer",
          customerEmail: "<EMAIL>",
          shop: "test.myshopify.com",
        },
      });

      expect(booking).toBeDefined();
      expect(booking.description).toBeNull();
      expect(booking.customerPhone).toBeNull();
      expect(booking.price).toBeNull();
      expect(booking.notes).toBeNull();
      expect(booking.currency).toBe("USD"); // Should default to USD
      expect(booking.status).toBe(BookingStatus.PENDING); // Should default to PENDING
    });
  });

  describe("Booking status management", () => {
    it("should allow updating booking status", async () => {
      const booking = await prisma.booking.create({
        data: {
          title: "Status Test Booking",
          startDateTime: new Date("2024-12-01T10:00:00Z"),
          endDateTime: new Date("2024-12-01T11:00:00Z"),
          bookingType: BookingType.HOURLY,
          customerName: "Status Tester",
          customerEmail: "<EMAIL>",
          shop: "status-test.myshopify.com",
        },
      });

      // Update to confirmed
      const confirmedBooking = await prisma.booking.update({
        where: { id: booking.id },
        data: { status: BookingStatus.CONFIRMED },
      });

      expect(confirmedBooking.status).toBe(BookingStatus.CONFIRMED);

      // Update to completed
      const completedBooking = await prisma.booking.update({
        where: { id: booking.id },
        data: { status: BookingStatus.COMPLETED },
      });

      expect(completedBooking.status).toBe(BookingStatus.COMPLETED);
    });
  });

  describe("Querying bookings", () => {
    it("should find bookings by shop", async () => {
      const shop1 = "shop1.myshopify.com";
      const shop2 = "shop2.myshopify.com";

      await prisma.booking.create({
        data: {
          title: "Shop 1 Booking",
          startDateTime: new Date("2024-12-01T10:00:00Z"),
          endDateTime: new Date("2024-12-01T11:00:00Z"),
          bookingType: BookingType.HOURLY,
          customerName: "Customer 1",
          customerEmail: "<EMAIL>",
          shop: shop1,
        },
      });

      await prisma.booking.create({
        data: {
          title: "Shop 2 Booking",
          startDateTime: new Date("2024-12-01T12:00:00Z"),
          endDateTime: new Date("2024-12-01T13:00:00Z"),
          bookingType: BookingType.HOURLY,
          customerName: "Customer 2",
          customerEmail: "<EMAIL>",
          shop: shop2,
        },
      });

      const shop1Bookings = await prisma.booking.findMany({
        where: { shop: shop1 },
      });

      expect(shop1Bookings).toHaveLength(1);
      expect(shop1Bookings[0].title).toBe("Shop 1 Booking");
    });

    it("should find bookings by booking type", async () => {
      await prisma.booking.create({
        data: {
          title: "Hourly Booking",
          startDateTime: new Date("2024-12-01T10:00:00Z"),
          endDateTime: new Date("2024-12-01T11:00:00Z"),
          bookingType: BookingType.HOURLY,
          customerName: "Hourly Customer",
          customerEmail: "<EMAIL>",
          shop: "test.myshopify.com",
        },
      });

      await prisma.booking.create({
        data: {
          title: "Full Day Booking",
          startDateTime: new Date("2024-12-01T00:00:00Z"),
          endDateTime: new Date("2024-12-01T23:59:59Z"),
          bookingType: BookingType.FULL_DAY,
          customerName: "Full Day Customer",
          customerEmail: "<EMAIL>",
          shop: "test.myshopify.com",
        },
      });

      const hourlyBookings = await prisma.booking.findMany({
        where: { bookingType: BookingType.HOURLY },
      });

      const fullDayBookings = await prisma.booking.findMany({
        where: { bookingType: BookingType.FULL_DAY },
      });

      expect(hourlyBookings).toHaveLength(1);
      expect(fullDayBookings).toHaveLength(1);
      expect(hourlyBookings[0].title).toBe("Hourly Booking");
      expect(fullDayBookings[0].title).toBe("Full Day Booking");
    });
  });

  describe("Data validation", () => {
    it("should require all mandatory fields", async () => {
      // This test verifies that Prisma enforces required fields
      await expect(
        prisma.booking.create({
          data: {
            // Missing required fields
            title: "Incomplete Booking",
          } as any,
        }),
      ).rejects.toThrow();
    });

    it("should handle decimal prices correctly", async () => {
      const booking = await prisma.booking.create({
        data: {
          title: "Decimal Price Test",
          startDateTime: new Date("2024-12-01T10:00:00Z"),
          endDateTime: new Date("2024-12-01T11:00:00Z"),
          bookingType: BookingType.HOURLY,
          customerName: "Price Tester",
          customerEmail: "<EMAIL>",
          shop: "price-test.myshopify.com",
          price: 99.99,
        },
      });

      expect(booking.price?.toString()).toBe("99.99");
    });
  });
});
