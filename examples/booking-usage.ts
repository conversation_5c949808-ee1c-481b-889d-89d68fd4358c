/**
 * Example usage of the Booking model and server functions
 * 
 * This file demonstrates how to use the booking system for both
 * hourly and full day bookings in a Shopify app context.
 */

import { BookingType, BookingStatus } from '@prisma/client'
import {
  createHourlyBooking,
  createFullDayBooking,
  getBookingsByShop,
  getBookingsByType,
  updateBooking,
  checkBookingConflicts
} from '../app/models/booking.server'

// Example: Creating an hourly booking
async function createExampleHourlyBooking() {
  const hourlyBooking = await createHourlyBooking({
    title: 'Conference Room A - Team Meeting',
    description: 'Weekly team standup and planning session',
    startDateTime: new Date('2024-12-15T10:00:00Z'),
    endDateTime: new Date('2024-12-15T11:30:00Z'),
    customerName: '<PERSON>',
    customerEmail: '<EMAIL>',
    customerPhone: '******-0123',
    shop: 'my-company.myshopify.com',
    price: 75.00,
    currency: 'USD',
    notes: 'Please set up projector and whiteboard'
  })

  console.log('Created hourly booking:', hourlyBooking.id)
  return hourlyBooking
}

// Example: Creating a full day booking
async function createExampleFullDayBooking() {
  const fullDayBooking = await createFullDayBooking({
    title: 'Event Hall - Company Retreat',
    description: 'Annual company retreat and team building activities',
    startDateTime: new Date('2024-12-20T00:00:00Z'),
    endDateTime: new Date('2024-12-20T23:59:59Z'),
    customerName: 'Sarah Johnson',
    customerEmail: '<EMAIL>',
    customerPhone: '******-0456',
    shop: 'my-company.myshopify.com',
    price: 500.00,
    currency: 'USD',
    notes: 'Full catering required, AV equipment setup needed'
  })

  console.log('Created full day booking:', fullDayBooking.id)
  return fullDayBooking
}

// Example: Getting all bookings for a shop
async function getShopBookings(shop: string) {
  const bookings = await getBookingsByShop(shop)
  
  console.log(`Found ${bookings.length} bookings for shop: ${shop}`)
  
  bookings.forEach(booking => {
    console.log(`- ${booking.title} (${booking.bookingType}) - ${booking.status}`)
  })
  
  return bookings
}

// Example: Getting bookings by type
async function getBookingsByTypeExample(shop: string) {
  const hourlyBookings = await getBookingsByType(shop, BookingType.HOURLY)
  const fullDayBookings = await getBookingsByType(shop, BookingType.FULL_DAY)
  
  console.log(`Hourly bookings: ${hourlyBookings.length}`)
  console.log(`Full day bookings: ${fullDayBookings.length}`)
  
  return { hourlyBookings, fullDayBookings }
}

// Example: Confirming a booking
async function confirmBooking(bookingId: string) {
  const updatedBooking = await updateBooking(bookingId, {
    status: BookingStatus.CONFIRMED
  })
  
  console.log(`Booking ${bookingId} confirmed`)
  return updatedBooking
}

// Example: Checking for booking conflicts
async function checkForConflicts(shop: string, startTime: Date, endTime: Date) {
  const conflicts = await checkBookingConflicts(shop, startTime, endTime)
  
  if (conflicts.length > 0) {
    console.log(`Found ${conflicts.length} conflicting bookings:`)
    conflicts.forEach(conflict => {
      console.log(`- ${conflict.title}: ${conflict.startDateTime} to ${conflict.endDateTime}`)
    })
    return true
  } else {
    console.log('No conflicts found')
    return false
  }
}

// Example: Complete booking workflow
async function completeBookingWorkflow() {
  const shop = 'example-shop.myshopify.com'
  
  try {
    // 1. Create some example bookings
    console.log('=== Creating Example Bookings ===')
    const hourlyBooking = await createExampleHourlyBooking()
    const fullDayBooking = await createExampleFullDayBooking()
    
    // 2. List all bookings for the shop
    console.log('\n=== Shop Bookings ===')
    await getShopBookings(shop)
    
    // 3. Get bookings by type
    console.log('\n=== Bookings by Type ===')
    await getBookingsByTypeExample(shop)
    
    // 4. Check for conflicts with a new booking
    console.log('\n=== Checking for Conflicts ===')
    const newBookingStart = new Date('2024-12-15T10:30:00Z')
    const newBookingEnd = new Date('2024-12-15T12:00:00Z')
    
    const hasConflicts = await checkForConflicts(shop, newBookingStart, newBookingEnd)
    
    if (!hasConflicts) {
      console.log('Safe to create new booking!')
    } else {
      console.log('Cannot create booking due to conflicts')
    }
    
    // 5. Confirm the bookings
    console.log('\n=== Confirming Bookings ===')
    await confirmBooking(hourlyBooking.id)
    await confirmBooking(fullDayBooking.id)
    
    console.log('\n=== Workflow Complete ===')
    
  } catch (error) {
    console.error('Error in booking workflow:', error)
  }
}

// Export functions for use in other parts of the application
export {
  createExampleHourlyBooking,
  createExampleFullDayBooking,
  getShopBookings,
  getBookingsByTypeExample,
  confirmBooking,
  checkForConflicts,
  completeBookingWorkflow
}

// Uncomment the line below to run the example workflow
// completeBookingWorkflow()
