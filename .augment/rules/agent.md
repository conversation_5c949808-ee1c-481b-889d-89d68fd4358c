---
type: "always_apply"
---

Try to break any given task to as many simple task as possible
Execute one simple task at a time
Follow a TTD style of coding
Write test for the requirement first before implementation
Change test only if the requirement changes
Don't change test to fix issues in implementation
Always test the implementation after testing
Always validate the code for syntax or type error and fix them
